class PlayerTrackerInfo extends GSInfo {
    function GetAuthor()        { return "Player Tracker Developer"; }
    function GetName()          { return "Player Tracker"; }
    function GetDescription()   { return "Tracks player connections and disconnections"; }
    function GetVersion()       { return 1; }
    function MinVersionToLoad() { return 1; }
    function GetDate()          { return "2025-07-18"; }
    function GetShortName()     { return "PLTR"; } // must be exactly 4 chars
    function CreateInstance()   { return "PlayerTracker"; }
    function GetAPIVersion()    { return "1.5"; } // match your OpenTTD version
}

/* Tell the core we are a Game Script */
RegisterGS(PlayerTrackerInfo());
