class MyGameScriptInfo extends GSInfo {
    function GetVersion()       { return 1; }
    function GetName()          { return "My First Game Script"; }
    function GetDescription()   { return "Sample script showing GS structure"; }
    function GetShortName()     { return "MGS1"; } // must be exactly 4 chars
    function GetDate()          { return "2025-07-18"; }
    function GetAPIVersion()    { return "1.5"; } // match your OpenTTD version
    function GetRequirements()  { return [ScriptFlag.Pause, ScriptFlag.TownGrowth]; }
}
RegisterGS(MyGameScriptInfo());
